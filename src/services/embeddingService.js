const fetch = require('node-fetch');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

class EmbeddingService {
  constructor() {
    // Initialize with fallback values until config is loaded
    this.openaiApiKey = process.env.OPENAI_API_KEY || '';

    // Use OpenAI for embeddings
    this.provider = 'openai';

    // Model configuration
    this.models = {
      openai: {
        model: 'text-embedding-ada-002',
        dimensions: 1536,
        baseUrl: 'https://api.openai.com/v1/embeddings'
      }
    };

    // Rate limiting tracker
    this.rateLimitTracker = {
      openai: {
        requestsToday: 0,
        requestsThisHour: 0,
        requestsThisMinute: 0,
        lastResetDay: new Date().getDate(),
        lastResetHour: new Date().getHours(),
        lastResetMinute: new Date().getMinutes()
      }
    };

    this.currentModel = this.models[this.provider];

    // Initialize configuration
    this.isConfigured = false;
    this.initializeConfig();
  }

  /**
   * Initialize configuration asynchronously
   */
  async initializeConfig() {
    try {
      const configManager = require('../config');

      // Try to get config, but don't fail if it's not ready yet
      if (configManager.isInitialized) {
        const config = configManager.getConfig();
        this.updateConfiguration(config);
      } else {
        // Config will be updated later when it's ready
        console.log('🔧 EmbeddingService: Using fallback values until configuration is loaded');
        this.checkConfiguration();
      }
    } catch (error) {
      console.log('🔧 EmbeddingService: Using environment variables for configuration');
      this.checkConfiguration();
    }
  }

  /**
   * Update configuration when it becomes available
   */
  updateConfiguration(config) {
    if (config && config.openai) {
      this.openaiApiKey = config.openai.apiKey || process.env.OPENAI_API_KEY || '';
      this.isConfigured = !!this.openaiApiKey;

      console.log('🔧 OpenAI Embedding Service Configuration Updated:');
      console.log(`   Provider: ${this.provider.toUpperCase()}`);
      console.log(`   Model: ${this.currentModel.model}`);
      console.log(`   Dimensions: ${this.currentModel.dimensions}`);
      console.log(`   OpenAI API Key: ${this.openaiApiKey ? '✅ Available' : '❌ REQUIRED'}`);

      if (!this.isConfigured) {
        console.error('❌ OpenAI API key required!');
        console.error('💡 Add OPENAI_API_KEY to environment variables or AWS Secrets Manager');
      } else {
        console.log(`✅ ${this.provider.toUpperCase()} embedding service configured`);
      }
    }
  }

  /**
   * Check initial configuration
   */
  checkConfiguration() {
    this.isConfigured = !!this.openaiApiKey;

    console.log('🔧 OpenAI Embedding Service Configuration:');
    console.log(`   Provider: ${this.provider.toUpperCase()}`);
    console.log(`   Model: ${this.currentModel.model}`);
    console.log(`   Dimensions: ${this.currentModel.dimensions}`);
    console.log(`   OpenAI API Key: ${this.openaiApiKey ? '✅ Available' : '❌ REQUIRED'}`);

    if (!this.isConfigured) {
      console.error('❌ OpenAI API key required!');
      console.error('💡 Add OPENAI_API_KEY to environment variables or AWS Secrets Manager');
    } else {
      console.log(`✅ ${this.provider.toUpperCase()} embedding service initialized`);
    }

    // Embedding hash cache configuration
    this.cacheConfig = {
      enabled: process.env.EMBEDDING_CACHE_ENABLED !== 'false', // Default enabled
      maxSize: parseInt(process.env.EMBEDDING_CACHE_MAX_SIZE) || 10000, // Max cached embeddings
      maxAge: parseInt(process.env.EMBEDDING_CACHE_MAX_AGE) || 7 * 24 * 60 * 60 * 1000, // 7 days in ms
      cacheFile: path.join(__dirname, '../../cache/embedding_cache.json'),
      persistInterval: 5 * 60 * 1000 // Save cache every 5 minutes
    };

    // In-memory cache
    this.embeddingCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      saves: 0,
      evictions: 0,
      lastPersist: null
    };

    // Initialize cache
    this.initializeCache();
    console.log(`💾 Embedding Cache: ${this.cacheConfig.enabled ? 'ENABLED' : 'DISABLED'} (max: ${this.cacheConfig.maxSize} entries)`);
  }

  /**
   * Initialize embedding cache from persistent storage
   */
  async initializeCache() {
    if (!this.cacheConfig.enabled) {
      return;
    }

    try {
      // Ensure cache directory exists
      const cacheDir = path.dirname(this.cacheConfig.cacheFile);
      await fs.mkdir(cacheDir, { recursive: true });

      // Load existing cache if it exists
      try {
        const cacheData = await fs.readFile(this.cacheConfig.cacheFile, 'utf8');
        const parsedCache = JSON.parse(cacheData);

        // Restore cache entries, filtering out expired ones
        const now = Date.now();
        let loadedCount = 0;
        let expiredCount = 0;

        for (const [key, entry] of Object.entries(parsedCache.entries || {})) {
          if (now - entry.timestamp < this.cacheConfig.maxAge) {
            this.embeddingCache.set(key, entry);
            loadedCount++;
          } else {
            expiredCount++;
          }
        }

        // Restore stats
        this.cacheStats = { ...this.cacheStats, ...(parsedCache.stats || {}) };

        console.log(`💾 Cache loaded: ${loadedCount} entries, ${expiredCount} expired entries removed`);
      } catch (error) {
        // Cache file doesn't exist or is corrupted - start fresh
        console.log(`💾 Starting with fresh cache (${error.code === 'ENOENT' ? 'no existing cache' : 'cache corrupted'})`);
      }

      // Set up periodic persistence
      this.setupCachePersistence();

    } catch (error) {
      console.warn(`⚠️ Cache initialization failed: ${error.message}`);
      this.cacheConfig.enabled = false;
    }
  }

  /**
   * Set up periodic cache persistence
   */
  setupCachePersistence() {
    if (!this.cacheConfig.enabled) {
      return;
    }

    setInterval(async () => {
      await this.persistCache();
    }, this.cacheConfig.persistInterval);

    // Also persist on process exit
    process.on('SIGINT', async () => {
      await this.persistCache();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await this.persistCache();
      process.exit(0);
    });
  }

  /**
   * Persist cache to disk
   */
  async persistCache() {
    if (!this.cacheConfig.enabled || this.embeddingCache.size === 0) {
      return;
    }

    try {
      const cacheData = {
        version: '1.0',
        timestamp: Date.now(),
        entries: Object.fromEntries(this.embeddingCache),
        stats: this.cacheStats
      };

      await fs.writeFile(this.cacheConfig.cacheFile, JSON.stringify(cacheData, null, 2));
      this.cacheStats.lastPersist = Date.now();
      console.log(`💾 Cache persisted: ${this.embeddingCache.size} entries`);
    } catch (error) {
      console.warn(`⚠️ Cache persistence failed: ${error.message}`);
    }
  }

  /**
   * Generate a hash for text content
   * @param {string} text - Text to hash
   * @returns {string} Hash string
   */
  generateTextHash(text) {
    // Normalize text for consistent hashing
    const normalizedText = text.trim().replace(/\s+/g, ' ').toLowerCase();

    // Include provider and model in hash to ensure cache validity
    const hashInput = `${this.provider}:${this.currentModel.model}:${normalizedText}`;

    return crypto.createHash('sha256').update(hashInput, 'utf8').digest('hex');
  }

  /**
   * Get embedding from cache
   * @param {string} text - Text to get embedding for
   * @returns {number[]|null} Cached embedding or null if not found
   */
  getCachedEmbedding(text) {
    if (!this.cacheConfig.enabled) {
      return null;
    }

    const hash = this.generateTextHash(text);
    const cached = this.embeddingCache.get(hash);

    if (cached) {
      // Check if cache entry is still valid
      const age = Date.now() - cached.timestamp;
      if (age < this.cacheConfig.maxAge) {
        this.cacheStats.hits++;
        console.log(`💾 Cache HIT for text hash: ${hash.substring(0, 12)}... (age: ${Math.round(age / 1000 / 60)} minutes)`);
        return cached.embedding;
      } else {
        // Remove expired entry
        this.embeddingCache.delete(hash);
        this.cacheStats.evictions++;
      }
    }

    this.cacheStats.misses++;
    return null;
  }

  /**
   * Store embedding in cache
   * @param {string} text - Text that was embedded
   * @param {number[]} embedding - Generated embedding
   */
  setCachedEmbedding(text, embedding) {
    if (!this.cacheConfig.enabled || !embedding) {
      return;
    }

    const hash = this.generateTextHash(text);

    // Check cache size limit
    if (this.embeddingCache.size >= this.cacheConfig.maxSize) {
      // Remove oldest entries (simple LRU)
      const oldestKey = this.embeddingCache.keys().next().value;
      this.embeddingCache.delete(oldestKey);
      this.cacheStats.evictions++;
    }

    this.embeddingCache.set(hash, {
      embedding,
      timestamp: Date.now(),
      textLength: text.length,
      provider: this.provider,
      model: this.currentModel.model
    });

    this.cacheStats.saves++;
    // console.log(`💾 Cache SAVE for text hash: ${hash.substring(0, 12)}... (cache size: ${this.embeddingCache.size})`);
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    const hitRate = this.cacheStats.hits + this.cacheStats.misses > 0
      ? (this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) * 100).toFixed(1)
      : 0;

    return {
      ...this.cacheStats,
      cacheSize: this.embeddingCache.size,
      maxSize: this.cacheConfig.maxSize,
      hitRate: `${hitRate}%`,
      enabled: this.cacheConfig.enabled
    };
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.embeddingCache.clear();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      saves: 0,
      evictions: 0,
      lastPersist: null
    };
    // console.log(`💾 Cache cleared`);
  }

  /**
   * Generate embeddings for text using OpenAI API
   * @param {string} text - Text to generate embeddings for
   * @returns {Promise<number[]>} Array of embedding values
   */
  async generateEmbeddings(text) {
    try {
      console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔍 GENERATING VECTOR EMBEDDINGS FOR USER QUERY`);
      console.log(`🔍 ═══════════════════════════════════════════════════════════════`);
      console.log(`📝 Query Text: "${text}"`);
      console.log(`📏 Text Length: ${text.length} characters`);
      console.log(`🤖 Provider: ${this.provider.toUpperCase()}`);
      console.log(`🤖 Model: ${this.currentModel.model}`);
      console.log(`📊 Expected Dimensions: ${this.currentModel.dimensions}`);

      // Check cache first
      const cachedEmbedding = this.getCachedEmbedding(text);
      if (cachedEmbedding) {
        // console.log(`💾 Using cached embedding - skipping API call`);
        // console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
        return cachedEmbedding;
      }

      if (!this.isConfigured) {
        // console.error(`❌ OpenAI API key required!`);
        // console.error(`🚫 Mock embeddings disabled`);
        // console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
        throw new Error('OpenAI API key required - no fallbacks');
      }

      // Use OpenAI for embeddings
      if (this.provider === 'openai') {
        // console.log(`🎯 Using OpenAI embeddings`);
        const embedding = await this.generateOpenAIEmbeddings(text);

        // Cache the result
        this.setCachedEmbedding(text, embedding);
        return embedding;
      } else {
        // console.log(`🎯 Using mock embeddings (no API keys)`);
        const mockEmbeddings = this.generateMockEmbeddings(text);
        // console.log(`✅ Generated mock embeddings: ${mockEmbeddings.length} dimensions`);
        // console.log(`📊 Sample values: [${mockEmbeddings.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
        // console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
        return mockEmbeddings;
      }



    } catch (error) {
      // console.error('❌ Failed to generate embeddings:', error.message);
      // console.error('🚫 No fallbacks enabled - OpenAI focus mode');
      // console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

      // Throw error instead of falling back to mock embeddings
      throw new Error(`OpenAI embedding failed: ${error.message}`);

      // Fallback to mock embeddings - COMMENTED OUT for OpenAI focus
      /*
      console.log('🔄 Falling back to mock embeddings');
      const mockEmbeddings = this.generateMockEmbeddings(text);
      console.log(`✅ Generated fallback mock embeddings: ${mockEmbeddings.length} dimensions`);
      console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);
      return mockEmbeddings;
      */
    }
  }



  /**
   * Generate embeddings using OpenAI API (single text)
   */
  async generateOpenAIEmbeddings(text) {
    // console.log(`🌐 Calling OpenAI Embeddings API (single)...`);
    const startTime = Date.now();

    const response = await fetch(this.models.openai.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.models.openai.model,
        input: text,
        encoding_format: "float",
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      // console.error(`❌ OpenAI API error: ${response.status} - ${errorText}`);
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const embeddings = data.data[0].embedding;
    const duration = Date.now() - startTime;

    console.log(`✅ Generated OpenAI embeddings in ${duration}ms`);
    console.log(`📊 Embedding dimensions: ${embeddings.length}`);
    console.log(`📊 Sample values: [${embeddings.slice(0, 5).map(v => v.toFixed(4)).join(', ')}...]`);
    console.log(`💰 Usage: ${data.usage?.total_tokens || 'unknown'} tokens`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    return embeddings;
  }

  /**
   * Generate embeddings using OpenAI API (batch processing)
   * @param {string[]} texts - Array of texts to embed
   * @returns {Promise<number[][]>} Array of embedding vectors
   */
  async generateOpenAIBatchEmbeddings(texts) {
    // console.log(`🚀 Calling OpenAI Batch Embeddings API for ${texts.length} texts...`);
    const startTime = Date.now();

    const response = await fetch(this.models.openai.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.models.openai.model,
        input: texts, // Send array of texts
        encoding_format: "float",
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ OpenAI Batch API error: ${response.status} - ${errorText}`);
      throw new Error(`OpenAI Batch API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const embeddings = data.data.map(item => item.embedding);
    const duration = Date.now() - startTime;

    console.log(`✅ Generated ${embeddings.length} OpenAI embeddings in ${duration}ms`);
    console.log(`📊 Embedding dimensions: ${embeddings[0].length}`);
    console.log(`📊 Average time per embedding: ${(duration / embeddings.length).toFixed(2)}ms`);
    console.log(`💰 Total usage: ${data.usage?.total_tokens || 'unknown'} tokens`);
    console.log(`🚀 Batch processing speedup: ~${Math.round(embeddings.length * 1000 / duration)}x faster`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    return embeddings;
  }

  /**
   * Generate mock embeddings for development/testing
   * This creates a simple hash-based embedding vector that's consistent
   * @param {string} text - Text to generate mock embeddings for
   * @returns {number[]} Array of mock embedding values
   */
  generateMockEmbeddings(text) {
    const dimension = this.currentModel.dimensions; // Match current provider dimensions
    const embeddings = new Array(dimension).fill(0);

    // Simple hash-based approach for consistent mock embeddings
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Generate pseudo-random but deterministic values
    for (let i = 0; i < dimension; i++) {
      const seed = hash + i;
      const x = Math.sin(seed) * 10000;
      embeddings[i] = (x - Math.floor(x)) * 2 - 1; // Normalize to [-1, 1]
    }

    // Normalize the vector to unit length
    const magnitude = Math.sqrt(embeddings.reduce((sum, val) => sum + val * val, 0));
    for (let i = 0; i < dimension; i++) {
      embeddings[i] = embeddings[i] / magnitude;
    }

    return embeddings;
  }

  /**
   * Calculate cosine similarity between two embedding vectors
   * @param {number[]} a - First embedding vector
   * @param {number[]} b - Second embedding vector
   * @returns {number} Cosine similarity score (0-1)
   */
  calculateCosineSimilarity(a, b) {
    if (a.length !== b.length) {
      throw new Error('Embedding vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    const similarity = dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    return Math.max(0, Math.min(1, similarity)); // Clamp to [0, 1]
  }

  /**
   * Check if API key is available
   */
  hasApiKey() {
    return this.isConfigured;
  }

  /**
   * Generate embeddings for multiple texts (optimized batch processing with parallel execution)
   * @param {string[]} texts - Array of texts to embed
   * @param {number} batchSize - Maximum number of texts per API call
   * @param {number} maxRetries - Maximum retries per batch (default: 3)
   * @param {boolean} useParallel - Enable parallel batch processing (default: true)
   * @returns {Promise<number[][]>} Array of embedding vectors
   */
  async generateBatchEmbeddings(texts, batchSize = null, maxRetries = 3, useParallel = true) {
    // OpenAI rate limit configuration
    const currentLimits = {
      rpm: 3000,  // Much higher limits
      rph: 180000,
      rpd: 4320000,
      batchSize: 75,  // Increased from 50 to 75
      delayBetweenBatches: 50, // Reduced delay
      maxParallelBatches: 5, // Allow more parallel processing
      adaptiveBatching: true
    };
    let effectiveBatchSize = batchSize || currentLimits.batchSize;

    // Adaptive batch sizing based on text length
    if (currentLimits.adaptiveBatching && !batchSize) {
      const avgTextLength = texts.reduce((sum, text) => sum + text.length, 0) / texts.length;
      if (avgTextLength > 2000) {
        effectiveBatchSize = Math.max(10, Math.floor(effectiveBatchSize * 0.7)); // Reduce for long texts
      } else if (avgTextLength < 500) {
        effectiveBatchSize = Math.floor(effectiveBatchSize * 1.3); // Increase for short texts
      }
    }

    console.log(`🚀 Starting optimized batch embedding generation for ${texts.length} texts`);
    console.log(`📦 Provider: ${this.provider}`);
    console.log(`📦 Optimized batch size: ${effectiveBatchSize} texts per API call`);
    console.log(`🔄 Max retries per batch: ${maxRetries}`);
    console.log(`⚡ Parallel processing: ${useParallel ? 'ENABLED' : 'DISABLED'}`);
    console.log(`⏱️ Rate limits: ${currentLimits.rpm} RPM, ${currentLimits.rph} RPH, ${currentLimits.rpd} RPD`);
    console.log(`⏳ Delay between batches: ${currentLimits.delayBetweenBatches}ms`);
    if (useParallel) {
      console.log(`🔀 Max parallel batches: ${currentLimits.maxParallelBatches}`);
    }

    const allEmbeddings = new Array(texts.length).fill(null);
    const totalBatches = Math.ceil(texts.length / effectiveBatchSize);
    const estimatedTime = useParallel
      ? (totalBatches * currentLimits.delayBetweenBatches) / (1000 * currentLimits.maxParallelBatches)
      : (totalBatches * currentLimits.delayBetweenBatches) / 1000;
    const failedBatches = [];
    let successfulBatches = 0;

    console.log(`📊 Total batches: ${totalBatches}`);
    console.log(`⏰ Estimated processing time: ${Math.ceil(estimatedTime / 60)} minutes (${useParallel ? 'with parallel processing' : 'sequential'})`);

    // Choose processing strategy based on useParallel flag
    if (useParallel && currentLimits.maxParallelBatches > 1) {
      return await this.processEmbeddingBatchesParallel(
        texts, effectiveBatchSize, maxRetries, currentLimits, allEmbeddings
      );
    } else {
      return await this.processEmbeddingBatchesSequential(
        texts, effectiveBatchSize, maxRetries, currentLimits, allEmbeddings
      );
    }
  }

  /**
   * Process embedding batches in parallel with rate limiting
   */
  async processEmbeddingBatchesParallel(texts, effectiveBatchSize, maxRetries, currentLimits, allEmbeddings) {
    const totalBatches = Math.ceil(texts.length / effectiveBatchSize);
    const failedBatches = [];
    let successfulBatches = 0;

    // Create batch jobs
    const batchJobs = [];
    for (let i = 0; i < texts.length; i += effectiveBatchSize) {
      const batch = texts.slice(i, i + effectiveBatchSize);
      const batchNumber = Math.floor(i / effectiveBatchSize) + 1;
      const batchStartIndex = i;

      batchJobs.push({
        batch,
        batchNumber,
        batchStartIndex,
        texts: batch
      });
    }

    console.log(`🔀 Processing ${batchJobs.length} batches with max ${currentLimits.maxParallelBatches} parallel workers`);

    // Process batches in parallel with controlled concurrency
    const semaphore = new Array(currentLimits.maxParallelBatches).fill(null);
    const processingPromises = [];

    for (let i = 0; i < batchJobs.length; i++) {
      const job = batchJobs[i];

      // Wait for available slot
      const slotIndex = i % currentLimits.maxParallelBatches;
      if (processingPromises[slotIndex]) {
        await processingPromises[slotIndex];
      }

      // Start processing this batch
      processingPromises[slotIndex] = this.processSingleBatch(
        job, maxRetries, currentLimits, allEmbeddings, failedBatches, totalBatches
      ).then(() => {
        successfulBatches++;
        console.log(`✅ Parallel batch ${job.batchNumber}/${totalBatches} completed (${successfulBatches}/${totalBatches})`);
      }).catch(error => {
        console.error(`❌ Parallel batch ${job.batchNumber} failed:`, error.message);
      });

      // Add delay between batch starts to respect rate limits
      if (i < batchJobs.length - 1) {
        await new Promise(resolve => setTimeout(resolve, currentLimits.delayBetweenBatches / currentLimits.maxParallelBatches));
      }
    }

    // Wait for all remaining batches to complete
    await Promise.all(processingPromises.filter(p => p));

    return this.finalizeBatchResults(texts, allEmbeddings, successfulBatches, totalBatches, failedBatches);
  }

  /**
   * Process embedding batches sequentially (original logic)
   */
  async processEmbeddingBatchesSequential(texts, effectiveBatchSize, maxRetries, currentLimits, allEmbeddings) {
    const totalBatches = Math.ceil(texts.length / effectiveBatchSize);
    const failedBatches = [];
    let successfulBatches = 0;

    for (let i = 0; i < texts.length; i += effectiveBatchSize) {
      const batch = texts.slice(i, i + effectiveBatchSize);
      const batchNumber = Math.floor(i / effectiveBatchSize) + 1;
      const batchStartIndex = i;

      console.log(`\n📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} texts)`);

      const job = { batch, batchNumber, batchStartIndex, texts: batch };
      await this.processSingleBatch(job, maxRetries, currentLimits, allEmbeddings, failedBatches, totalBatches);
      successfulBatches++;

      // Rate limiting delay between batches (only if not the last batch)
      if (batchNumber < totalBatches) {
        console.log(`⏳ Rate limiting: waiting ${currentLimits.delayBetweenBatches}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, currentLimits.delayBetweenBatches));
      }
    }

    return this.finalizeBatchResults(texts, allEmbeddings, successfulBatches, totalBatches, failedBatches);
  }

  /**
   * Process a single batch with retry logic and cache integration
   */
  async processSingleBatch(job, maxRetries, currentLimits, allEmbeddings, failedBatches, totalBatches) {
    const { batch, batchNumber, batchStartIndex } = job;
    let batchSuccess = false;
    let retryCount = 0;

    // Check cache for each text in the batch
    const cacheResults = batch.map((text, index) => ({
      text,
      index: batchStartIndex + index,
      cached: this.getCachedEmbedding(text)
    }));

    const cachedCount = cacheResults.filter(r => r.cached).length;
    const uncachedTexts = cacheResults.filter(r => !r.cached);

    console.log(`💾 Batch ${batchNumber}: ${cachedCount}/${batch.length} cached, ${uncachedTexts.length} need API calls`);

    // Store cached embeddings immediately
    cacheResults.forEach(result => {
      if (result.cached) {
        allEmbeddings[result.index] = result.cached;
      }
    });

    // If all embeddings were cached, we're done
    if (uncachedTexts.length === 0) {
      console.log(`✅ Batch ${batchNumber}/${totalBatches} completed from cache`);
      return;
    }

    // Process uncached texts
    while (!batchSuccess && retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          // console.log(`🔄 Retry attempt ${retryCount}/${maxRetries} for batch ${batchNumber} (${uncachedTexts.length} uncached texts)`);
        }

        let newEmbeddings;
        const textsToEmbed = uncachedTexts.map(r => r.text);

        if (this.provider === 'openai') {
          newEmbeddings = await this.generateOpenAIBatchEmbeddings(textsToEmbed);
        } else {
          // For mock provider, generate embeddings one by one
          newEmbeddings = textsToEmbed.map(text => this.generateMockEmbeddings(text));
        }

        // Validate embeddings
        if (!newEmbeddings || newEmbeddings.length !== uncachedTexts.length) {
          throw new Error(`Invalid embeddings: expected ${uncachedTexts.length}, got ${newEmbeddings?.length || 0}`);
        }

        // Store new embeddings and cache them
        uncachedTexts.forEach((result, j) => {
          const embedding = newEmbeddings[j];
          allEmbeddings[result.index] = embedding;

          // Cache the new embedding
          this.setCachedEmbedding(result.text, embedding);
        });

        batchSuccess = true;
        console.log(`✅ Batch ${batchNumber}/${totalBatches} completed (${cachedCount} cached + ${uncachedTexts.length} new)`);

      } catch (error) {
        retryCount++;
        console.error(`❌ Batch ${batchNumber} attempt ${retryCount} failed:`, error.message);

        if (retryCount <= maxRetries) {
          // Determine retry delay based on error type
          let retryDelay = 1000; // Default 1 second

          if (error.message.includes('rate limit') || error.message.includes('429')) {
            retryDelay = 60000; // 60 seconds for rate limit
            console.log(`🚦 Rate limit error detected, waiting ${retryDelay / 1000} seconds...`);
          } else if (error.message.includes('timeout') || error.message.includes('network')) {
            retryDelay = 5000; // 5 seconds for network issues
            console.log(`🌐 Network error detected, waiting ${retryDelay / 1000} seconds...`);
          } else if (error.message.includes('server') || error.message.includes('500')) {
            retryDelay = 10000; // 10 seconds for server errors
            console.log(`🖥️ Server error detected, waiting ${retryDelay / 1000} seconds...`);
          } else {
            console.log(`🔄 Unknown error, waiting ${retryDelay / 1000} seconds...`);
          }

          await new Promise(resolve => setTimeout(resolve, retryDelay));
        } else {
          // Max retries exceeded - mark uncached texts as failed
          console.error(`💥 Batch ${batchNumber} failed after ${maxRetries} retries`);
          failedBatches.push({
            batchNumber,
            batchStartIndex,
            batch: uncachedTexts.map(r => r.text),
            error: error.message
          });

          // Fill uncached positions with null embeddings
          uncachedTexts.forEach(result => {
            allEmbeddings[result.index] = null;
          });

          batchSuccess = true; // Exit retry loop
        }
      }
    }
  }

  /**
   * Finalize batch processing results with comprehensive reporting
   */
  finalizeBatchResults(texts, allEmbeddings, successfulBatches, totalBatches, failedBatches) {

    // Summary report
    console.log(`\n🎉 Optimized batch embedding generation completed!`);
    console.log(`📊 Total embeddings requested: ${texts.length}`);
    console.log(`✅ Successful batches: ${successfulBatches}/${totalBatches}`);
    console.log(`❌ Failed batches: ${failedBatches.length}/${totalBatches}`);

    // Cache statistics
    const cacheStats = this.getCacheStats();
    // console.log(`\n💾 Cache Performance:`);
    // console.log(`   Cache hits: ${cacheStats.hits}`);
    // console.log(`   Cache misses: ${cacheStats.misses}`);
    // console.log(`   Hit rate: ${cacheStats.hitRate}`);
    // console.log(`   Cache size: ${cacheStats.cacheSize}/${cacheStats.maxSize}`);
    // console.log(`   Cost savings: ${cacheStats.hits > 0 ? Math.round((cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100) : 0}% API calls avoided`);

    if (failedBatches.length > 0) {
      console.log(`\n💥 Failed batch details:`);
      failedBatches.forEach(failed => {
        console.log(`   Batch ${failed.batchNumber}: ${failed.error}`);
      });

      // Filter out null embeddings for return
      const validEmbeddings = allEmbeddings.filter(emb => emb !== null);
      console.log(`📊 Valid embeddings generated: ${validEmbeddings.length}/${texts.length}`);

      if (validEmbeddings.length === 0) {
        throw new Error(`All batches failed. No embeddings generated.`);
      }

      // Return partial results with warning
      console.log(`⚠️ Returning partial results. Some embeddings may be missing.`);
      return allEmbeddings; // Includes nulls to maintain index alignment
    }

    console.log(`📊 Embedding dimensions: ${allEmbeddings[0]?.length || 'unknown'}`);
    console.log(`🚀 Performance: ${Math.round(successfulBatches / totalBatches * 100)}% success rate`);
    return allEmbeddings;
  }

  /**
   * Check and update rate limit tracking
   */
  checkRateLimits(provider) {
    const now = new Date();
    const tracker = this.rateLimitTracker[provider];

    // Reset counters if time periods have passed
    if (now.getDate() !== tracker.lastResetDay) {
      tracker.requestsToday = 0;
      tracker.lastResetDay = now.getDate();
    }

    if (now.getHours() !== tracker.lastResetHour) {
      tracker.requestsThisHour = 0;
      tracker.lastResetHour = now.getHours();
    }

    if (now.getMinutes() !== tracker.lastResetMinute) {
      tracker.requestsThisMinute = 0;
      tracker.lastResetMinute = now.getMinutes();
    }

    // OpenAI rate limits
    const currentLimits = { rpm: 3000, rph: 180000, rpd: 4320000 };

    // Check if we're approaching limits
    const usage = {
      minute: { current: tracker.requestsThisMinute, limit: currentLimits.rpm },
      hour: { current: tracker.requestsThisHour, limit: currentLimits.rph },
      day: { current: tracker.requestsToday, limit: currentLimits.rpd }
    };

    // Log current usage
    console.log(`📊 Rate limit usage for ${provider}:`);
    console.log(`   Minute: ${usage.minute.current}/${usage.minute.limit} (${Math.round(usage.minute.current / usage.minute.limit * 100)}%)`);
    console.log(`   Hour: ${usage.hour.current}/${usage.hour.limit} (${Math.round(usage.hour.current / usage.hour.limit * 100)}%)`);
    console.log(`   Day: ${usage.day.current}/${usage.day.limit} (${Math.round(usage.day.current / usage.day.limit * 100)}%)`);

    // Check if we can make another request
    const canMakeRequest =
      tracker.requestsThisMinute < currentLimits.rpm &&
      tracker.requestsThisHour < currentLimits.rph &&
      tracker.requestsToday < currentLimits.rpd;

    if (!canMakeRequest) {
      const blockedBy = [];
      if (tracker.requestsThisMinute >= currentLimits.rpm) blockedBy.push('minute limit');
      if (tracker.requestsThisHour >= currentLimits.rph) blockedBy.push('hour limit');
      if (tracker.requestsToday >= currentLimits.rpd) blockedBy.push('day limit');

      throw new Error(`Rate limit exceeded: ${blockedBy.join(', ')}`);
    }

    return { canMakeRequest, usage };
  }

  /**
   * Record a successful API request
   */
  recordRequest(provider) {
    const tracker = this.rateLimitTracker[provider];
    tracker.requestsThisMinute++;
    tracker.requestsThisHour++;
    tracker.requestsToday++;
  }

  /**
   * Get current provider information
   */
  getProviderInfo() {
    return {
      provider: this.provider,
      model: this.currentModel.model,
      dimensions: this.currentModel.dimensions,
      isConfigured: this.isConfigured,
      rateLimitUsage: this.rateLimitTracker[this.provider]
    };
  }

  /**
   * Split text into chunks for processing
   * @param {string} text - Text to split
   * @param {number} chunkSize - Size of each chunk
   * @param {number} overlap - Overlap between chunks
   * @returns {string[]} Array of text chunks
   */
  splitTextIntoChunks(text, chunkSize = 1000, overlap = 100) {
    const chunks = [];
    let start = 0;

    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length);
      const chunk = text.slice(start, end);
      chunks.push(chunk);

      if (end === text.length) break;
      start = end - overlap;
    }

    return chunks;
  }
}

module.exports = new EmbeddingService();
