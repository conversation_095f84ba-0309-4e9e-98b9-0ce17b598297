require('dotenv').config();
const awsSecretsManager = require('../services/awsSecretsManagerService');

// Configuration class to handle async initialization with AWS Secrets Manager
class ConfigManager {
  constructor() {
    this.config = null;
    this.isInitialized = false;
  }

  /**
   * Initialize configuration with AWS Secrets Manager
   * This should be called during application startup
   */
  async initialize() {
    if (this.isInitialized) {
      return this.config;
    }

    try {
      console.log('🔧 Initializing configuration with AWS Secrets Manager...');

      // Get secrets from AWS Secrets Manager (with environment variable fallback)
      const [apiKeys, serviceUrls] = await Promise.all([
        awsSecretsManager.getApiKeysConfig(),
        awsSecretsManager.getServiceUrlsConfig()
      ]);

      this.config = {
        // Server Configuration
        port: process.env.PORT || 3001,
        nodeEnv: process.env.NODE_ENV || 'development',

        // AWS Secrets Manager Configuration
        aws: {
          region: process.env.AWS_REGION || 'us-east-1',
          secretName: process.env.AWS_SECRET_NAME || 'chatai-service-secrets',
          isConfigured: awsSecretsManager.isConfigured
        },

        // User Service Configuration
        userService: {
          url: serviceUrls.userServiceUrl,
        },

        // ChatAI Origin for key validation
        chatAiOrigin: serviceUrls.chatAiOrigin,

        // Qdrant Vector Database Configuration
        qdrant: {
          url: serviceUrls.qdrantUrl,
          apiKey: apiKeys.qdrantApiKey || null,
          collectionName: process.env.QDRANT_COLLECTION || 'chatai_documents',
        },

        // LlamaIndex Cloud Configuration (kept for fallback)
        llamaIndex: {
          apiKey: apiKeys.llamaCloudApiKey || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp',
          baseUrl: 'https://api.cloud.llamaindex.ai/api/v1',
        },

        // OpenRouter Configuration
        openRouter: {
          apiKey: apiKeys.openRouterApiKey || '',
          baseUrl: 'https://openrouter.ai/api/v1',
          model: 'mistralai/mistral-nemo:free',
        },

        // OpenAI Configuration (for embeddings)
        openai: {
          apiKey: apiKeys.openaiApiKey || '',
          embeddingModel: 'text-embedding-ada-002',
          baseUrl: 'https://api.openai.com/v1',
        },

        // Internal API Key for service-to-service communication
        internal: {
          apiKey: apiKeys.internalApiKey || 'chatai-internal-2024',
        },

        // Cache Configuration
        cache: {
          ttlMinutes: parseInt(process.env.CACHE_TTL_MINUTES) || 15,
          maxSessions: parseInt(process.env.MAX_SESSIONS) || 1000,
          cleanupIntervalMinutes: parseInt(process.env.CLEANUP_INTERVAL_MINUTES) || 5,
        },

        // Rate Limiting
        rateLimit: {
          windowMinutes: parseInt(process.env.RATE_LIMIT_WINDOW_MINUTES) || 15,
          maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
        },

        // Logging
        logLevel: process.env.LOG_LEVEL || 'info',
      };

      // Perform validation
      await this.validateConfiguration();

      this.isInitialized = true;
      console.log('✅ Configuration initialized successfully');
      console.log(`🔐 AWS Secrets Manager: ${awsSecretsManager.isConfigured ? 'Enabled' : 'Disabled (using env vars)'}`);

      return this.config;
    } catch (error) {
      console.error('❌ Failed to initialize configuration:', error.message);
      throw error;
    }
  }

  /**
   * Validate configuration and check required secrets
   */
  async validateConfiguration() {
    const requiredSecrets = [
      'LLAMA_CLOUD_API_KEY',
      'OPENROUTER_API_KEY',
    ];

    const recommendedSecrets = [
      'OPENAI_API_KEY',
    ];

    // Validate secrets using AWS Secrets Manager service
    const validation = await awsSecretsManager.validateSecrets([...requiredSecrets, ...recommendedSecrets]);

    const missingRequired = requiredSecrets.filter(key => !validation.available.includes(key));
    const missingRecommended = recommendedSecrets.filter(key => !validation.available.includes(key));

    if (missingRequired.length > 0) {
      console.error('❌ Missing required secrets:', missingRequired);
      console.error('💡 These secrets must be configured in AWS Secrets Manager or environment variables');
      process.exit(1);
    }

    if (missingRecommended.length > 0) {
      console.warn('⚠️ Missing recommended secrets for semantic search:', missingRecommended);
      console.warn('💡 Without OPENAI_API_KEY, the system will use mock embeddings');
    }

    console.log(`✅ Configuration validation passed: ${validation.available.length}/${validation.total} secrets available`);
  }

  /**
   * Get configuration (ensure it's initialized first)
   */
  getConfig() {
    if (!this.isInitialized) {
      throw new Error('Configuration not initialized. Call initialize() first.');
    }
    return this.config;
  }

  /**
   * Refresh configuration by clearing cache and re-initializing
   */
  async refresh() {
    console.log('🔄 Refreshing configuration...');
    awsSecretsManager.clearCache();
    this.isInitialized = false;
    this.config = null;
    return this.initialize();
  }

  /**
   * Get health status of configuration services
   */
  async getHealthStatus() {
    const awsHealth = await awsSecretsManager.healthCheck();

    return {
      configuration: {
        initialized: this.isInitialized,
        timestamp: new Date().toISOString()
      },
      awsSecretsManager: awsHealth
    };
  }
}

// Create singleton instance
const configManager = new ConfigManager();

// Export both the manager and a helper function for backwards compatibility
module.exports = configManager;

// For backwards compatibility, also export a synchronous version
// that throws an error if not initialized
module.exports.config = new Proxy({}, {
  get(target, prop) {
    const config = configManager.getConfig();
    return config[prop];
  }
});
