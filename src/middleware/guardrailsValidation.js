const fetch = require('node-fetch');
const semanticValidationService = require('../services/semanticValidationService');
const outOfDomainResponseService = require('../services/outOfDomainResponseService');

/**
 * Enhanced Guardrails validation middleware with semantic-first validation
 * Validates user input with context awareness
 */
class GuardrailsValidationMiddleware {
    constructor() {
        this.guardrailsServiceUrl = process.env.GUARDRAILS_SERVICE_URL || 'http://localhost:8001';
        this.enabled = process.env.GUARDRAILS_ENABLED === 'true';
        this.validationType = process.env.GUARDRAILS_VALIDATION_TYPE || 'profanity';
        this.model = process.env.GUARDRAILS_MODEL || 'mistralai/mistral-nemo:free';  // Match main service model
        this.timeout = parseInt(process.env.GUARDRAILS_TIMEOUT) || 10000; // Increased to 10s for LLM-based validation
        this.apiKey = process.env.GUARDRAILS_API_KEY || 'guardrails-secret-key-2024';
    }

    /**
     * Get authentication headers
     * @returns {Object} Headers with API key
     */
    getAuthHeaders() {
        return {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
        };
    }

    /**
     * Enhanced semantic-aware validation
     * @param {string} content - Content to validate
     * @param {string} appId - Application ID for semantic context
     * @param {Array} documents - Available documents
     * @returns {Promise<Object>} Enhanced validation result
     */
    async validateContentWithSemantics(content, appId, documents = []) {
        if (!this.enabled) {
            return {
                is_valid: true,
                validated_content: content,
                validation_errors: [],
                bypassed: true,
                semantic_bypass: false
            };
        }

        try {
            console.log('🔍 SEMANTIC-FIRST VALIDATION FLOW:');
            console.log(`   Content: "${content}"`);
            console.log(`   AppId: ${appId}`);

            // Step 1: Semantic relevance check
            const semanticResult = await semanticValidationService.checkSemanticRelevance(content, appId, documents);

            // Step 2: Handle out-of-domain queries
            if (semanticResult.outOfDomain && semanticResult.outOfDomain.isOutOfDomain) {
                console.log('🚫 OUT-OF-DOMAIN QUERY DETECTED:');
                console.log(`   Confidence: ${semanticResult.outOfDomain.confidence.toFixed(3)}`);
                console.log(`   Reasoning: ${semanticResult.outOfDomain.reasoning}`);

                return this.handleOutOfDomainQuery(content, semanticResult);
            }

            // Step 3: Check if we can bypass guardrails for high-confidence domain queries
            if (semanticValidationService.shouldBypassGuardrails(semanticResult, content)) {
                console.log('✅ SEMANTIC BYPASS: High-confidence domain query approved');
                return {
                    is_valid: true,
                    validated_content: content,
                    validation_errors: [],
                    bypassed: false,
                    semantic_bypass: true,
                    semantic_score: semanticResult.semanticScore,
                    relevance_level: semanticResult.relevanceLevel,
                    out_of_domain: semanticResult.outOfDomain
                };
            }

            // Step 4: Enhanced guardrails validation with context
            const validationResult = await this.validateContentWithContext(content, semanticResult.guardrailsContext);

            console.log('🔍 DEBUG: validateContentWithContext returned:', {
                threat_level: validationResult.threat_level,
                confidence_score: validationResult.confidence_score,
                sanitization_applied: validationResult.sanitization_applied,
                is_valid: validationResult.is_valid
            });

            // Add semantic metadata to result
            validationResult.semantic_score = semanticResult.semanticScore;
            validationResult.relevance_level = semanticResult.relevanceLevel;
            validationResult.semantic_bypass = false;
            validationResult.out_of_domain = semanticResult.outOfDomain;

            console.log('🔍 DEBUG: Final validationResult being returned:', {
                threat_level: validationResult.threat_level,
                confidence_score: validationResult.confidence_score,
                sanitization_applied: validationResult.sanitization_applied,
                is_valid: validationResult.is_valid,
                semantic_score: validationResult.semantic_score,
                relevance_level: validationResult.relevance_level
            });

            return validationResult;

        } catch (error) {
            console.error('❌ Semantic-aware validation failed:', error.message);
            // Fallback to basic validation
            return await this.validateContent(content);
        }
    }

    /**
     * Handle out-of-domain queries with appropriate responses
     * @param {string} content - Original query content
     * @param {Object} semanticResult - Semantic analysis result
     * @returns {Object} Validation result for out-of-domain query
     */
    handleOutOfDomainQuery(content, semanticResult) {
        const outOfDomainResult = semanticResult.outOfDomain;

        // Extract AI context from semantic result
        const aiContext = {
            ai_domain: semanticResult.guardrailsContext?.ai_domain || 'general',
            ai_role: semanticResult.guardrailsContext?.ai_role || 'general_assistant'
        };

        // Use the out-of-domain response service for intelligent responses with AI context
        const response = outOfDomainResponseService.generateResponse(content, outOfDomainResult, semanticResult, aiContext);
        const validationErrors = outOfDomainResponseService.generateValidationErrors(response);

        console.log('🚫 OUT-OF-DOMAIN RESPONSE GENERATED:');
        console.log(`   Message: ${response.message}`);
        console.log(`   Suggestions: ${response.suggestions.length}`);
        console.log(`   Confidence: ${response.confidence.toFixed(3)}`);

        // Check if this might be a misunderstood in-domain query
        const mightBeMisunderstood = outOfDomainResponseService.mightBeMisunderstood(content, outOfDomainResult);
        if (mightBeMisunderstood) {
            console.log('⚠️ Query might be misunderstood in-domain query - providing alternative suggestions');
            const alternatives = outOfDomainResponseService.generateAlternativeQueries(content);
            validationErrors.push(`Alternative questions you might try: ${alternatives.slice(0, 2).join(' or ')}`);
        }

        return {
            is_valid: false,
            validated_content: content,
            validation_errors: validationErrors,
            bypassed: false,
            semantic_bypass: false,
            semantic_score: semanticResult.semanticScore,
            relevance_level: 'out-of-domain',
            out_of_domain: outOfDomainResult,
            threat_level: 'low', // Out-of-domain is not a security threat
            confidence_score: outOfDomainResult.confidence,
            sanitization_applied: false,
            response_metadata: {
                might_be_misunderstood: mightBeMisunderstood,
                query_intent: response.query_intent,
                suggested_alternatives: mightBeMisunderstood ? outOfDomainResponseService.generateAlternativeQueries(content) : []
            }
        };
    }

    /**
     * Validate content with enhanced context
     * @param {string} content - Content to validate
     * @param {Object} context - Semantic context for validation
     * @returns {Promise<Object>} Validation result
     */
    async validateContentWithContext(content, context = {}) {
        const requestPayload = {
            content,
            model: this.model,
            validation_type: this.validationType,
            // Enhanced context
            semantic_score: context.semantic_score,
            ai_domain: context.ai_domain,
            ai_role: context.ai_role,
            has_relevant_content: context.has_relevant_content,
            retrieved_documents: context.retrieved_documents
        };

        console.log('🚀 Enhanced Guardrails Service Request DEBUG:');
        console.log('   URL:', `${this.guardrailsServiceUrl}/validate/context-aware`);
        console.log('   Method: POST');
        console.log('   Headers:', this.getAuthHeaders());
        console.log('   Payload Details:');
        console.log('     - content:', `"${content}"`);
        console.log('     - model:', `"${this.model}"`);
        console.log('     - validation_type:', `"${this.validationType}"`);
        console.log('     - semantic_score:', context.semantic_score, '(type:', typeof context.semantic_score, ')');
        console.log('     - ai_domain:', `"${context.ai_domain}"`, '(type:', typeof context.ai_domain, ')');
        console.log('     - ai_role:', `"${context.ai_role}"`, '(type:', typeof context.ai_role, ')');
        console.log('     - has_relevant_content:', context.has_relevant_content, '(type:', typeof context.has_relevant_content, ')');
        console.log('     - retrieved_documents:', context.retrieved_documents ? context.retrieved_documents.length : 'null', 'items');
        console.log('   Full Context Object:', JSON.stringify(context, null, 2));
        console.log('   Full Payload:', JSON.stringify(requestPayload, null, 2));

        try {
            const startTime = Date.now();
            const response = await fetch(`${this.guardrailsServiceUrl}/validate/context-aware`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(requestPayload),
                timeout: this.timeout
            });

            const duration = Date.now() - startTime;
            console.log(`⏱️ Enhanced guardrails validation completed in ${duration}ms`);

            if (!response.ok) {
                throw new Error(`Guardrails service error: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('✅ Enhanced Guardrails Service Response:', {
                status: response.status,
                statusText: response.statusText,
                result: result
            });

            // Extract the actual validation result from the response
            const validationResult = result.result || result;

            // Merge metadata into the main result for compatibility
            if (validationResult.metadata) {
                Object.assign(validationResult, validationResult.metadata);
            }

            console.log('🔍 EXTRACTED VALIDATION RESULT:', {
                has_validated_content: !!validationResult.validated_content,
                validated_content: validationResult.validated_content,
                threat_level: validationResult.threat_level,
                sanitization_applied: validationResult.sanitization_applied,
                is_valid: validationResult.is_valid
            });

            return validationResult;

        } catch (error) {
            console.error('❌ Enhanced guardrails validation failed:', error.message);
            // Fallback to basic validation
            return await this.validateContent(content);
        }
    }

    /**
     * Validate content using Guardrails service (original method)
     * @param {string} content - Content to validate
     * @returns {Promise<Object>} Validation result
     */
    async validateContent(content) {
        if (!this.enabled) {
            return {
                is_valid: true,
                validated_content: content,
                validation_errors: [],
                bypassed: true
            };
        }

        // Log request parameters being sent to guardrails service
        const requestPayload = {
            content,
            model: this.model,
            validation_type: this.validationType
        };

        console.log('🚀 Guardrails Service Request:', {
            url: `${this.guardrailsServiceUrl}/validate`,
            method: 'POST',
            headers: this.getAuthHeaders(),
            payload: requestPayload
        });

        try {
            const startTime = Date.now();
            const response = await fetch(`${this.guardrailsServiceUrl}/validate`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(requestPayload),
                timeout: this.timeout
            });

            const duration = Date.now() - startTime;
            console.log(`⏱️ Guardrails validation completed in ${duration}ms (timeout: ${this.timeout}ms)`);

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Guardrails service authentication failed - check API key');
                } else if (response.status === 403) {
                    throw new Error('Guardrails service access denied - invalid API key');
                } else {
                    throw new Error(`Guardrails service error: ${response.status} ${response.statusText}`);
                }
            }

            const result = await response.json();

            // Log response received from guardrails service
            console.log('✅ Guardrails Service Response:', {
                status: response.status,
                statusText: response.statusText,
                result: result
            });

            return result;
        } catch (error) {
            const isTimeout = error.message.includes('timeout') || error.code === 'ETIMEDOUT';
            const errorType = isTimeout ? 'TIMEOUT' : 'ERROR';

            console.error(`❌ Guardrails validation ${errorType}:`, {
                message: error.message,
                timeout: this.timeout,
                isTimeout,
                contentLength: content.length
            });

            // Fail safe - if validation service is down, allow content but log warning
            return {
                is_valid: true,
                validated_content: content,
                validation_errors: [`Validation service unavailable: ${errorType.toLowerCase()} at: ${this.guardrailsServiceUrl}/validate`],
                bypassed: true
            };
        }
    }

    /**
     * Validate multiple content items in batch
     * @param {Array<string>} contents - Array of content to validate
     * @returns {Promise<Object>} Batch validation result
     */
    async validateBatchContent(contents) {
        if (!this.enabled) {
            return {
                results: contents.map(content => ({
                    original_content: content,
                    is_valid: true,
                    validated_content: content,
                    validation_errors: [],
                    bypassed: true
                })),
                total_processed: contents.length,
                bypassed: true
            };
        }

        // Log batch request parameters being sent to guardrails service
        const batchRequestPayload = {
            contents,
            model: this.model,
            validation_type: this.validationType
        };

        console.log('🚀 Guardrails Batch Service Request:', {
            url: `${this.guardrailsServiceUrl}/validate/batch`,
            method: 'POST',
            headers: this.getAuthHeaders(),
            payload: batchRequestPayload
        });

        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/validate/batch`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(batchRequestPayload),
                timeout: this.timeout
            });

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Guardrails service authentication failed - check API key');
                } else if (response.status === 403) {
                    throw new Error('Guardrails service access denied - invalid API key');
                } else {
                    throw new Error(`Guardrails service error: ${response.status} ${response.statusText}`);
                }
            }

            const result = await response.json();

            // Log batch response received from guardrails service
            console.log('✅ Guardrails Batch Service Response:', {
                status: response.status,
                statusText: response.statusText,
                result: result
            });

            return result;
        } catch (error) {
            console.error('❌ Guardrails batch validation failed:', error.message);

            // Fail safe - if validation service is down, allow all content but log warning
            return {
                results: contents.map(content => ({
                    original_content: content,
                    is_valid: true,
                    validated_content: content,
                    validation_errors: [`Validation service unavailable: ${error.message}`],
                    bypassed: true
                })),
                total_processed: contents.length,
                bypassed: true
            };
        }
    }

    /**
     * Enhanced semantic-first validation middleware
     * @param {Object} options - Validation options
     * @returns {Function} Express middleware
     */
    validateRequestWithSemantics(options = {}) {
        return async (req, res, next) => {
            try {
                // Skip validation for non-POST requests
                if (req.method !== 'POST') {
                    return next();
                }

                // Extract content from request body
                const content = req.body.query || req.body.message || req.body.content;

                if (!content) {
                    return next();
                }

                // Get app context from request (set by API validation)
                const appId = req.validationData?.result?.appId;
                const documents = req.validationData?.result?.documents || [];

                if (!appId) {
                    console.warn('⚠️ No appId found for semantic validation, falling back to basic validation');
                    return this.validateRequest()(req, res, next);
                }

                // Perform semantic-aware validation
                const validationResult = await this.validateContentWithSemantics(content, appId, documents);

                // Log validation result
                if (!validationResult.is_valid) {
                    console.warn('Content validation failed:', {
                        content: content.substring(0, 100) + '...',
                        errors: validationResult.validation_errors,
                        semantic_score: validationResult.semantic_score,
                        relevance_level: validationResult.relevance_level,
                        userAgent: req.get('User-Agent'),
                        ip: req.ip
                    });
                }

                // Handle semantic bypass
                if (validationResult.semantic_bypass) {
                    console.log('✅ SEMANTIC BYPASS: Query approved without guardrails check');
                    req.semanticBypass = true;
                    return next();
                }

                // Always replace content with validated content (even if it's sanitized)
                if (validationResult.validated_content !== content) {
                    if (req.body.query) req.body.query = validationResult.validated_content;
                    if (req.body.message) req.body.message = validationResult.validated_content;
                    if (req.body.content) req.body.content = validationResult.validated_content;

                    console.log('Content sanitized by Enhanced Guardrails:', {
                        original: content.substring(0, 50) + '...',
                        sanitized: validationResult.validated_content.substring(0, 50) + '...',
                        semantic_score: validationResult.semantic_score
                    });
                }

                // Enhanced validation decision logic
                const validationDecision = this.makeValidationDecision(validationResult, content);

                if (validationDecision.action === 'REJECT') {
                    console.error('🚫 Content rejected by enhanced guardrails:', {
                        reason: validationDecision.reason,
                        semantic_score: validationResult.semantic_score,
                        relevance_level: validationResult.relevance_level,
                        validation_errors: validationResult.validation_errors
                    });

                    req.guardrailsRejection = {
                        reason: validationDecision.reason,
                        validation_errors: validationResult.validation_errors,
                        validated_content: validationResult.validated_content,
                        semantic_score: validationResult.semantic_score,
                        relevance_level: validationResult.relevance_level,
                        threat_level: validationResult.threat_level,
                        confidence_score: validationResult.confidence_score,
                        sanitization_applied: validationResult.sanitization_applied
                    };
                }

                if (validationDecision.action === 'PROCEED_SANITIZED') {
                    console.info('🧹 Content sanitized by semantic-aware guardrails:', {
                        original: content.substring(0, 50) + '...',
                        sanitized: validationResult.validated_content.substring(0, 50) + '...',
                        semantic_score: validationResult.semantic_score
                    });
                    // Content has been sanitized and replaced in req.body above (lines 431-441)
                    // No need to set req.guardrailsRejection - let it proceed normally
                }

                if (validationDecision.action === 'PROCEED_WITH_WARNING') {
                    console.warn('⚠️ Content proceeding with warning from semantic-aware guardrails:', {
                        reason: validationDecision.reason,
                        semantic_score: validationResult.semantic_score,
                        threat_level: validationResult.threat_level
                    });
                }

                // Store validation context for system prompt enhancement
                if (validationResult.validation_errors && validationResult.validation_errors.length > 0) {
                    req.guardrailsValidation = {
                        validation_errors: validationResult.validation_errors,
                        sanitized_content: validationResult.validated_content,
                        semantic_score: validationResult.semantic_score,
                        relevance_level: validationResult.relevance_level
                    };
                }

                return next();

            } catch (error) {
                console.error('❌ Enhanced validation middleware error:', error.message);
                // Fallback to basic validation
                return this.validateRequest()(req, res, next);
            }
        };
    }

    /**
     * Express middleware for validating request body (original method)
     */
    validateRequest() {
        return async (req, res, next) => {
            try {
                // Skip validation for non-POST requests
                if (req.method !== 'POST') {
                    return next();
                }

                // Extract content from request body
                const content = req.body.query || req.body.message || req.body.content;

                if (!content) {
                    return next();
                }

                // Validate the content
                const validationResult = await this.validateContent(content);

                // Log validation result
                if (!validationResult.is_valid) {
                    console.warn('Content validation failed:', {
                        content: content.substring(0, 100) + '...',
                        errors: validationResult.validation_errors,
                        userAgent: req.get('User-Agent'),
                        ip: req.ip
                    });
                }

                // Always replace content with validated content (even if it's sanitized)
                if (validationResult.validated_content !== content) {
                    if (req.body.query) req.body.query = validationResult.validated_content;
                    if (req.body.message) req.body.message = validationResult.validated_content;
                    if (req.body.content) req.body.content = validationResult.validated_content;

                    console.log('Content sanitized by Guardrails:', {
                        original: content.substring(0, 50) + '...',
                        sanitized: validationResult.validated_content.substring(0, 50) + '...'
                    });
                }

                // Enhanced validation decision logic based on threat level and sanitization
                const validationDecision = this.makeValidationDecision(validationResult, content);

                if (validationDecision.action === 'REJECT') {
                    const extractedThreatLevel = validationResult.threat_level || validationResult.metadata?.threat_level;
                    console.error('🚫 Content rejected by guardrails:', {
                        reason: validationDecision.reason,
                        threat_level: extractedThreatLevel,
                        confidence_score: validationResult.confidence_score,
                        validation_errors: validationResult.validation_errors
                    });

                    // Store rejection info in request for consistent response format
                    req.guardrailsRejection = {
                        reason: validationDecision.reason,
                        validation_errors: validationResult.validation_errors,
                        validated_content: validationResult.validated_content,
                        threat_level: extractedThreatLevel,
                        confidence_score: validationResult.confidence_score,
                        sanitization_applied: validationResult.sanitization_applied,
                        relevance_level: validationResult.relevance_level || 'medium'
                    };

                    // Continue to next middleware - let the main handler create consistent response format
                    return next();
                }

                if (validationDecision.action === 'PROCEED_WITH_WARNING') {
                    const extractedThreatLevel = validationResult.threat_level || validationResult.metadata?.threat_level;
                    console.warn('⚠️ Content proceeding with warning:', {
                        reason: validationDecision.reason,
                        threat_level: extractedThreatLevel,
                        confidence_score: validationResult.confidence_score
                    });
                }

                if (validationDecision.action === 'PROCEED_SANITIZED') {
                    const extractedThreatLevel = validationResult.threat_level || validationResult.metadata?.threat_level;
                    console.info('🧹 Content proceeding with sanitization:', {
                        reason: validationDecision.reason,
                        threat_level: extractedThreatLevel,
                        original_length: content.length,
                        sanitized_length: validationResult.validated_content.length
                    });
                }

                // Add validation metadata to request
                req.guardrailsValidation = {
                    is_valid: validationResult.is_valid,
                    validation_errors: validationResult.validation_errors,
                    bypassed: validationResult.bypassed || false,
                    confidence_score: validationResult.confidence_score,
                    threat_level: validationResult.threat_level || validationResult.metadata?.threat_level,
                    sanitization_applied: validationResult.sanitization_applied || validationResult.metadata?.sanitization_applied,
                    metadata: validationResult.metadata,
                    original_content: content,
                    sanitized_content: validationResult.validated_content,
                    validation_action: validationDecision.action,
                    validation_reason: validationDecision.reason
                };

                next();
            } catch (error) {
                console.error('Guardrails middleware error:', error);
                // Continue processing even if validation fails
                next();
            }
        };
    }

    /**
     * Make validation decision based on threat level, sanitization, and confidence
     * @param {Object} validationResult - Result from guardrails service
     * @param {string} originalContent - Original content before validation
     * @returns {Object} Decision object with action and reason
     */
    makeValidationDecision(validationResult, originalContent) {
        const {
            is_valid,
            threat_level,
            sanitization_applied,
            confidence_score,
            validated_content,
            bypassed
        } = validationResult;

        // If validation was bypassed (service down), allow with warning
        if (bypassed) {
            return {
                action: 'PROCEED_WITH_WARNING',
                reason: 'Validation service unavailable - proceeding with caution'
            };
        }

        // If content is valid, proceed normally
        if (is_valid) {
            return {
                action: 'PROCEED',
                reason: 'Content passed validation'
            };
        }

        // Handle invalid content based on threat level
        const threatLevel = (threat_level || validationResult.metadata?.threat_level || '').toLowerCase();
        const sanitized = sanitization_applied === true || validationResult.metadata?.sanitization_applied === true;
        const contentWasSanitized = validated_content && validated_content !== originalContent;
        const confidence = confidence_score || 0;

        switch (threatLevel) {
            case 'high':
                // High threat: always reject regardless of sanitization
                return {
                    action: 'REJECT',
                    reason: 'Content contains high-threat violations and cannot be processed'
                };

            case 'medium':
                if (sanitized || contentWasSanitized) {
                    return {
                        action: 'PROCEED_SANITIZED',
                        reason: 'Medium-threat content sanitized and allowed to proceed'
                    };
                } else {
                    return {
                        action: 'REJECT',
                        reason: 'Medium-threat content cannot be sanitized safely'
                    };
                }

            case 'low':
                if (sanitized || contentWasSanitized) {
                    return {
                        action: 'PROCEED_SANITIZED',
                        reason: 'Low-threat content sanitized for safety'
                    };
                } else if (confidence >= 0.7) {
                    return {
                        action: 'PROCEED_WITH_WARNING',
                        reason: 'Low-threat content proceeding with monitoring'
                    };
                } else {
                    return {
                        action: 'PROCEED',
                        reason: 'Low-threat, low-confidence content allowed'
                    };
                }

            default:
                // Fallback for unknown threat levels - use conservative approach
                if (sanitized || contentWasSanitized) {
                    return {
                        action: 'PROCEED_SANITIZED',
                        reason: 'Content sanitized due to validation concerns'
                    };
                } else if (confidence >= 0.8) {
                    return {
                        action: 'REJECT',
                        reason: 'High-confidence validation failure - content rejected'
                    };
                } else {
                    return {
                        action: 'PROCEED_WITH_WARNING',
                        reason: 'Validation failed but proceeding with caution due to low confidence'
                    };
                }
        }
    }

    /**
     * Check if Guardrails service is healthy
     * @returns {Promise<boolean>} Health status
     */
    async checkHealth() {
        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/health`, {
                method: 'GET',
                timeout: 3000
            });

            return response.ok;
        } catch (error) {
            console.error('Guardrails health check failed:', error.message);
            return false;
        }
    }

    /**
     * Get available validators
     * @returns {Promise<Array>} List of available validators
     */
    async getValidators() {
        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/validators`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                timeout: 3000
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    throw new Error('Authentication failed - check API key');
                }
                throw new Error(`Guardrails service error: ${response.status}`);
            }

            const result = await response.json();
            return result.validators || [];
        } catch (error) {
            console.error('Failed to get validators:', error.message);
            return [];
        }
    }

    /**
     * Get Guardrails service configuration
     * @returns {Promise<Object>} Configuration info
     */
    async getConfig() {
        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/config`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                timeout: 3000
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    throw new Error('Authentication failed - check API key');
                }
                throw new Error(`Guardrails service error: ${response.status}`);
            }

            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Failed to get config:', error.message);
            return null;
        }
    }
}

// Create singleton instance
const guardrailsValidation = new GuardrailsValidationMiddleware();

module.exports = guardrailsValidation;